<script>
  import { page } from '$app/stores';
  import { goto } from '$app/navigation';
  import { enhance } from '$app/forms';

  /** @type {import('./$types').PageData} */
  export let data;

  /** @type {import('./$types').ActionData} */
  export let form;

  // Component state
  let selectedProductValidityGroup = data.selectedProductValidityGroup || '';
  let selectedItems = new Set();
  let showAddForm = false;
  let newItem = {
    productDesignation: '',
    serviceCode: '',
    actionType: '',
    activityPurpose: '',
    serviceActivityLabel: '',
    partNumber: 0,
    unitOfMeasure: '',
    quantity: 0,
    accHours: 0,
    frequency: 1000,
    sequenceNumber: 1
  };

  // Get data from server
  $: items = data.items || [];
  $: productValidityGroups = data.productValidityGroups || [];
  $: filteredItems = selectedProductValidityGroup
    ? items.filter(item => item.productDesignation === selectedProductValidityGroup)
        .sort((a, b) => a.accHours - b.accHours)
    : [];

  // Handle ProductValidityGroup selection
  function handleProductValidityGroupChange() {
    selectedItems.clear();
    selectedItems = selectedItems; // Trigger reactivity

    // Navigate to the same page with the selected ProductValidityGroup as a URL parameter
    if (selectedProductValidityGroup) {
      goto(`/service-plan-product-designation?productValidityGroup=${encodeURIComponent(selectedProductValidityGroup)}`);
    } else {
      goto('/service-plan-product-designation');
    }
  }

  // Handle item selection
  function toggleItemSelection(itemId) {
    if (selectedItems.has(itemId)) {
      selectedItems.delete(itemId);
    } else {
      selectedItems.add(itemId);
    }
    selectedItems = selectedItems; // Trigger reactivity
  }

  // Navigate to detail page
  function viewItemDetails(itemId) {
    goto(`/service-plan-product-designation/${itemId}`);
  }

  // Handle bulk delete
  async function handleBulkDelete() {
    if (selectedItems.size === 0) return;

    if (!confirm(`Are you sure you want to delete ${selectedItems.size} selected items?`)) {
      return;
    }

    try {
      const response = await fetch('/api/service-plan-product-designation', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          itemIds: Array.from(selectedItems)
        })
      });

      if (response.ok) {
        // Refresh the page to show updated data
        window.location.reload();
      } else {
        alert('Error deleting items');
      }
    } catch (error) {
      console.error('Error deleting items:', error);
      alert('Error deleting items');
    }
  }

  // Toggle add form
  function toggleAddForm() {
    showAddForm = !showAddForm;
    if (showAddForm) {
      newItem.productDesignation = selectedProductValidityGroup;
    }
  }

  // Reset form
  function resetForm() {
    newItem = {
      productDesignation: '',
      serviceCode: '',
      actionType: '',
      activityPurpose: '',
      serviceActivityLabel: '',
      partNumber: 0,
      unitOfMeasure: '',
      quantity: 0,
      accHours: 0,
      frequency: 1000,
      sequenceNumber: 1
    };
    showAddForm = false;
  }
</script>

<svelte:head>
  <title>Service Plan Product Designation</title>
</svelte:head>

<div class="container">
  <div class="header-content">
    <h1>Service Plan Product Designation</h1>
    <div class="header-actions">
      <a href="/" class="btn-secondary">← Back to Home</a>
    </div>
  </div>

  <!-- Product Validity Group Selection -->
  <div class="filter-section">
    <div class="filter-group">
      <label for="productValidityGroup">Select Product Validity Group:</label>
      <select
        id="productValidityGroup"
        bind:value={selectedProductValidityGroup}
        on:change={handleProductValidityGroupChange}
        class="filter-select"
      >
        <option value="">-- Select Product Validity Group --</option>
        {#each productValidityGroups as group}
          <option value={group}>{group}</option>
        {/each}
      </select>
    </div>
  </div>

  {#if selectedProductValidityGroup}
    <!-- Action buttons -->
    <div class="action-section">
      <div class="action-buttons">
        <button
          class="btn-primary"
          on:click={toggleAddForm}
        >
          {showAddForm ? 'Cancel' : 'Add New Item'}
        </button>

        {#if selectedItems.size > 0}
          <button
            class="btn-danger"
            on:click={handleBulkDelete}
          >
            Delete Selected ({selectedItems.size})
          </button>
        {/if}
      </div>

      <div class="info-text">
        Showing {filteredItems.length} items for {selectedProductValidityGroup} (sorted by hours)
      </div>
    </div>

    <!-- Add new item form -->
    {#if showAddForm}
      <div class="add-form-section">
        <h3>Add New Service Plan Item</h3>
        <form method="POST" action="?/create" use:enhance>
          <input type="hidden" name="productDesignation" value={selectedProductValidityGroup} />

          <div class="form-grid">
            <div class="form-group">
              <label for="serviceCode">Service Code:</label>
              <input
                type="text"
                id="serviceCode"
                name="serviceCode"
                bind:value={newItem.serviceCode}
                required
              />
            </div>

            <div class="form-group">
              <label for="actionType">Action Type:</label>
              <input
                type="text"
                id="actionType"
                name="actionType"
                bind:value={newItem.actionType}
                required
              />
            </div>

            <div class="form-group">
              <label for="activityPurpose">Activity Purpose:</label>
              <input
                type="text"
                id="activityPurpose"
                name="activityPurpose"
                bind:value={newItem.activityPurpose}
              />
            </div>

            <div class="form-group">
              <label for="serviceActivityLabel">Service Activity Label:</label>
              <input
                type="text"
                id="serviceActivityLabel"
                name="serviceActivityLabel"
                bind:value={newItem.serviceActivityLabel}
              />
            </div>

            <div class="form-group">
              <label for="partNumber">Part Number:</label>
              <input
                type="number"
                id="partNumber"
                name="partNumber"
                bind:value={newItem.partNumber}
              />
            </div>

            <div class="form-group">
              <label for="unitOfMeasure">Unit of Measure:</label>
              <input
                type="text"
                id="unitOfMeasure"
                name="unitOfMeasure"
                bind:value={newItem.unitOfMeasure}
              />
            </div>

            <div class="form-group">
              <label for="quantity">Quantity:</label>
              <input
                type="number"
                id="quantity"
                name="quantity"
                bind:value={newItem.quantity}
              />
            </div>

            <div class="form-group">
              <label for="accHours">Accumulated Hours:</label>
              <input
                type="number"
                id="accHours"
                name="accHours"
                bind:value={newItem.accHours}
                required
              />
            </div>

            <div class="form-group">
              <label for="frequency">Frequency (Hours):</label>
              <input
                type="number"
                id="frequency"
                name="frequency"
                bind:value={newItem.frequency}
                required
              />
            </div>

            <div class="form-group">
              <label for="sequenceNumber">Sequence Number:</label>
              <input
                type="number"
                id="sequenceNumber"
                name="sequenceNumber"
                bind:value={newItem.sequenceNumber}
                required
              />
            </div>
          </div>

          <div class="form-actions">
            <button type="submit" class="btn-primary">Create Item</button>
            <button type="button" class="btn-secondary" on:click={resetForm}>Cancel</button>
          </div>
        </form>
      </div>
    {/if}

    <!-- Items list -->
    <div class="items-section">
      {#if filteredItems.length === 0}
        <div class="no-items">
          No service plan items found for the selected Product Validity Group.
        </div>
      {:else}
        <div class="items-table">
          <table>
            <thead>
              <tr>
                <th>
                  <input
                    type="checkbox"
                    on:change={(e) => {
                      const target = e.target;
                      if (target && target.checked) {
                        selectedItems = new Set(filteredItems.map(item => item._id));
                      } else {
                        selectedItems.clear();
                        selectedItems = selectedItems;
                      }
                    }}
                  />
                </th>
                <th>Hours</th>
                <th>Service Code</th>
                <th>Action Type</th>
                <th>Activity Purpose</th>
                <th>Part Number</th>
                <th>Frequency</th>
                <th>Sequence</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {#each filteredItems as item}
                <tr class:selected={selectedItems.has(item._id)}>
                  <td>
                    <input
                      type="checkbox"
                      checked={selectedItems.has(item._id)}
                      on:change={() => toggleItemSelection(item._id)}
                    />
                  </td>
                  <td class="hours-cell">{item.accHours}</td>
                  <td>{item.serviceCode}</td>
                  <td>{item.actionType}</td>
                  <td>{item.activityPurpose || '-'}</td>
                  <td>{item.partNumber || '-'}</td>
                  <td>{item.frequency}</td>
                  <td>{item.sequenceNumber}</td>
                  <td>
                    <button
                      class="btn-small btn-primary"
                      on:click={() => viewItemDetails(item._id)}
                    >
                      View Details
                    </button>
                  </td>
                </tr>
              {/each}
            </tbody>
          </table>
        </div>
      {/if}
    </div>
  {:else}
    <div class="no-selection">
      Please select a Product Validity Group to view service plan items.
    </div>
  {/if}
</div>

<style>
  .container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 2rem;
  }

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #e5e7eb;
  }

  .header-content h1 {
    font-size: 2rem;
    font-weight: 700;
    color: #1f2937;
    margin: 0;
  }

  .filter-section {
    background: #f9fafb;
    padding: 1.5rem;
    border-radius: 8px;
    margin-bottom: 2rem;
  }

  .filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .filter-group label {
    font-weight: 600;
    color: #374151;
  }

  .filter-select {
    padding: 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 1rem;
    max-width: 400px;
  }

  .action-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: #f3f4f6;
    border-radius: 8px;
  }

  .action-buttons {
    display: flex;
    gap: 1rem;
  }

  .info-text {
    color: #6b7280;
    font-weight: 500;
  }

  .add-form-section {
    background: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 2rem;
    margin-bottom: 2rem;
  }

  .add-form-section h3 {
    margin: 0 0 1.5rem 0;
    color: #1f2937;
    font-size: 1.25rem;
    font-weight: 600;
  }

  .form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
  }

  .form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .form-group label {
    font-weight: 600;
    color: #374151;
    font-size: 0.875rem;
  }

  .form-group input {
    padding: 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 1rem;
  }

  .form-group input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  .form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-start;
  }

  .items-table {
    background: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    overflow: hidden;
  }

  .items-table table {
    width: 100%;
    border-collapse: collapse;
  }

  .items-table th {
    background: #f9fafb;
    padding: 1rem;
    text-align: left;
    font-weight: 600;
    color: #374151;
    border-bottom: 1px solid #e5e7eb;
  }

  .items-table td {
    padding: 1rem;
    border-bottom: 1px solid #f3f4f6;
  }

  .items-table tr:hover {
    background: #f9fafb;
  }

  .items-table tr.selected {
    background: #eff6ff;
  }

  .hours-cell {
    font-weight: 600;
    color: #059669;
  }

  .no-items, .no-selection {
    text-align: center;
    padding: 3rem;
    color: #6b7280;
    font-size: 1.125rem;
  }

  /* Button styles */
  .btn-primary {
    background: #1e40af;
    color: white;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.2s;
  }

  .btn-primary:hover {
    background: #1d4ed8;
  }

  .btn-secondary {
    background: #6b7280;
    color: white;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    text-decoration: none;
    display: inline-block;
    transition: background-color 0.2s;
  }

  .btn-secondary:hover {
    background: #4b5563;
  }

  .btn-danger {
    background: #dc2626;
    color: white;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.2s;
  }

  .btn-danger:hover {
    background: #b91c1c;
  }

  .btn-small {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
  }
</style>
