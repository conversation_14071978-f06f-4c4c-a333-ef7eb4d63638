<script>
  import { page } from '$app/stores';
  import { goto } from '$app/navigation';
  import QuotationGrid from '$lib/components/grids/QuotationGrid.svelte';
  
  /** @type {import('./$types').PageData} */
  export let data;
  
  let selectedPackages = {
    baseContract: [],
    repairPackages: [],
    supportServices: [],
    replacementServices: []
  };
  
  let showSuccessMessage = false;
  let successMessage = '';
  let showErrorMessage = false;
  let errorMessage = '';
  
  // Initialize selected packages from data
  $: if (data.quotationPackages) {
    selectedPackages = {
      baseContract: data.quotationPackages.baseContract
        .filter(p => p.includeInOffer)
        .map(p => p.id),
      repairPackages: data.quotationPackages.repairPackages
        .filter(p => p.includeInOffer)
        .map(p => p.id),
      supportServices: data.quotationPackages.supportServices
        .filter(p => p.includeInOffer)
        .map(p => p.id),
      replacementServices: data.quotationPackages.replacementServices
        .filter(p => p.includeInOffer)
        .map(p => p.id)
    };
  }
  
  // Format date for display
  function formatDate(dateString) {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString();
  }
  
  // Toggle package selection in a section
  function togglePackage(section, event) {
    const { id } = event.detail;
    
    // Find the index of the ID in the selected packages
    const index = selectedPackages[section].indexOf(id);
    
    // Toggle the selection
    if (index > -1) {
      selectedPackages[section] = selectedPackages[section].filter(p => p !== id);
    } else {
      selectedPackages[section] = [...selectedPackages[section], id];
    }
    
    // Update the data
    if (section === 'baseContract') {
      data.quotationPackages.baseContract = data.quotationPackages.baseContract.map(p => ({
        ...p, 
        includeInOffer: p.required || selectedPackages.baseContract.includes(p.id)
      }));
    } else if (section === 'repairPackages') {
      data.quotationPackages.repairPackages = data.quotationPackages.repairPackages.map(p => ({
        ...p, 
        includeInOffer: p.required || selectedPackages.repairPackages.includes(p.id)
      }));
    } else if (section === 'supportServices') {
      data.quotationPackages.supportServices = data.quotationPackages.supportServices.map(p => ({
        ...p, 
        includeInOffer: p.required || selectedPackages.supportServices.includes(p.id)
      }));
    } else if (section === 'replacementServices') {
      data.quotationPackages.replacementServices = data.quotationPackages.replacementServices.map(p => ({
        ...p, 
        includeInOffer: p.required || selectedPackages.replacementServices.includes(p.id)
      }));
    }
  }
  
  // Calculate totals for different sections
  $: baseContractTotal = calculateSectionTotal(data.quotationPackages?.baseContract || []);
  $: repairPackagesTotal = calculateSectionTotal(data.quotationPackages?.repairPackages || []);
  $: supportServicesTotal = calculateSectionTotal(data.quotationPackages?.supportServices || []);
  $: replacementServicesTotal = calculateSectionTotal(data.quotationPackages?.replacementServices || []);
  $: grandTotal = baseContractTotal + repairPackagesTotal + supportServicesTotal + replacementServicesTotal;
  
  function calculateSectionTotal(packages) {
    return packages
      .filter(p => p.includeInOffer)
      .reduce((total, p) => total + (parseFloat(p.cost) || 0), 0);
  }
  
  // Save the quotation
  async function saveQuotation() {
    try {
      console.log('==========================================');
      console.log('CLIENT: STARTING QUOTATION SAVE PROCESS');
      console.log('==========================================');
      
      showSuccessMessage = false;
      showErrorMessage = false;
      
      console.log('CLIENT: Validating computer data');
      if (!data.computer || !data.computer._id) {
        console.error('CLIENT ERROR: Computer data is missing');
        throw new Error('Computer data is missing');
      }
      
      // Clone and prepare the data to avoid modifying the original
      console.log('CLIENT: Preparing data for submission');
      const preparedData = {
        computerId: data.computer._id,
        customerName: data.computer.customer || '',
        customerType: data.computer.customerType || '',
        baseContract: [],
        repairPackages: [],
        supportServices: [],
        replacementServices: [],
        totalCost: grandTotal,
        basicInfo: JSON.parse(JSON.stringify(data.quotationPackages.basicInfo || {})),
        createdAt: new Date()
      };
      
      console.log('CLIENT: Processing base contract items');
      // Filter and clean up included items
      if (Array.isArray(data.quotationPackages.baseContract)) {
        preparedData.baseContract = data.quotationPackages.baseContract
          .filter(item => item && item.includeInOffer === true)
          .map(item => ({
            id: item.id,
            name: item.name || '',
            activity: item.activity || '',
            cost: parseFloat(item.cost || 0),
            oemImporter: !!item.oemImporter,
            fleetOwner: !!item.fleetOwner,
            customerSpecific: !!item.customerSpecific,
            required: !!item.required,
            includeInOffer: true
          }));
        console.log(`CLIENT: Found ${preparedData.baseContract.length} base contract items`);
      } else {
        console.log('CLIENT: No base contract items found or not an array');
      }
      
      console.log('CLIENT: Processing repair package items');
      if (Array.isArray(data.quotationPackages.repairPackages)) {
        preparedData.repairPackages = data.quotationPackages.repairPackages
          .filter(item => item && item.includeInOffer === true)
          .map(item => ({
            id: item.id,
            name: item.name || '',
            activity: item.activity || '',
            cost: parseFloat(item.cost || 0),
            oemImporter: !!item.oemImporter,
            fleetOwner: !!item.fleetOwner,
            customerSpecific: !!item.customerSpecific,
            required: !!item.required,
            includeInOffer: true
          }));
        console.log(`CLIENT: Found ${preparedData.repairPackages.length} repair package items`);
      } else {
        console.log('CLIENT: No repair package items found or not an array');
      }
      
      console.log('CLIENT: Processing support service items');
      if (Array.isArray(data.quotationPackages.supportServices)) {
        preparedData.supportServices = data.quotationPackages.supportServices
          .filter(item => item && item.includeInOffer === true)
          .map(item => ({
            id: item.id,
            name: item.name || '',
            subname: item.subname || '',
            cost: parseFloat(item.cost || 0),
            oemImporter: !!item.oemImporter,
            fleetOwner: !!item.fleetOwner,
            customerSpecific: !!item.customerSpecific,
            required: !!item.required,
            includeInOffer: true
          }));
        console.log(`CLIENT: Found ${preparedData.supportServices.length} support service items`);
      } else {
        console.log('CLIENT: No support service items found or not an array');
      }
      
      console.log('CLIENT: Processing replacement service items');
      if (Array.isArray(data.quotationPackages.replacementServices)) {
        preparedData.replacementServices = data.quotationPackages.replacementServices
          .filter(item => item && item.includeInOffer === true)
          .map(item => ({
            id: item.id,
            name: item.name || '',
            subname: item.subname || '',
            cost: parseFloat(item.cost || 0),
            oemImporter: !!item.oemImporter,
            fleetOwner: !!item.fleetOwner,
            customerSpecific: !!item.customerSpecific,
            required: !!item.required,
            includeInOffer: true
          }));
        console.log(`CLIENT: Found ${preparedData.replacementServices.length} replacement service items`);
      } else {
        console.log('CLIENT: No replacement service items found or not an array');
      }
      
      console.log('CLIENT: Data preparation complete');
      console.log('CLIENT: Computer ID:', preparedData.computerId);
      console.log('CLIENT: Total cost:', preparedData.totalCost);
      
      console.log('CLIENT: Creating form data');
      const formData = new FormData();
      formData.append('quotationData', JSON.stringify(preparedData));
      
      console.log('CLIENT: Sending HTTP request');
      const response = await fetch(`?/updateQuotation`, {
        method: 'POST',
        body: formData
      });
      
      console.log('CLIENT: Received response, status:', response.status);
      if (!response.ok) {
        console.error(`CLIENT ERROR: Server returned ${response.status}: ${response.statusText}`);
        throw new Error(`Server returned ${response.status}: ${response.statusText}`);
      }
      
      console.log('CLIENT: Parsing response JSON');
      const result = await response.json();
      console.log('CLIENT: Response data:', result);
      
      if (result.success) {
        console.log('CLIENT: Save successful, quotation ID:', result.quotationId);
        showSuccessMessage = true;
        successMessage = result.message || 'Quotation saved successfully';
        setTimeout(() => {
          showSuccessMessage = false;
        }, 5000);
      } else {
        console.error('CLIENT ERROR: Server returned error:', result.message);
        throw new Error(result.message || 'Failed to save quotation');
      }
      
      console.log('CLIENT: SAVE PROCESS COMPLETE');
      console.log('==========================================');
    } catch (error) {
      console.error('==========================================');
      console.error('CLIENT ERROR: QUOTATION SAVE FAILED');
      console.error('Error details:', error);
      console.error('==========================================');
      
      showErrorMessage = true;
      errorMessage = error.message || 'An error occurred while saving the quotation';
      setTimeout(() => {
        showErrorMessage = false;
      }, 5000);
    }
  }
  
  // Go back to computer details
  function goBack() {
    goto(`/computer-id/${$page.params.id}`);
  }
  
  // Add package to quotation list
  function addPackageToQuotation() {
    // Implementation would depend on customer requirements
    alert('This feature is not yet implemented');
  }
  
  // Clear the quotation list
  function clearQuotationList() {
    data.quotationPackages.baseContract = data.quotationPackages.baseContract.map(p => ({
      ...p,
      includeInOffer: p.required
    }));
    
    data.quotationPackages.repairPackages = data.quotationPackages.repairPackages.map(p => ({
      ...p,
      includeInOffer: p.required
    }));
    
    data.quotationPackages.supportServices = data.quotationPackages.supportServices.map(p => ({
      ...p,
      includeInOffer: p.required
    }));
    
    data.quotationPackages.replacementServices = data.quotationPackages.replacementServices.map(p => ({
      ...p,
      includeInOffer: p.required
    }));
    
    // Update selected packages
    selectedPackages = {
      baseContract: data.quotationPackages.baseContract
        .filter(p => p.includeInOffer)
        .map(p => p.id),
      repairPackages: data.quotationPackages.repairPackages
        .filter(p => p.includeInOffer)
        .map(p => p.id),
      supportServices: data.quotationPackages.supportServices
        .filter(p => p.includeInOffer)
        .map(p => p.id),
      replacementServices: data.quotationPackages.replacementServices
        .filter(p => p.includeInOffer)
        .map(p => p.id)
    };
  }
  
  // Clear inputs
  function clearInputs() {
    // Reset basic info fields that are editable
    if (data.quotationPackages && data.quotationPackages.basicInfo) {
      data.quotationPackages.basicInfo.desiredContractLength = data.computer?.desiredContractLength || 12;
      data.quotationPackages.basicInfo.contractNumber = '';
      data.quotationPackages.basicInfo.primarySpare = false;
      data.quotationPackages.basicInfo.labourRate = 0;
    }
  }
</script>

<div class="container">
  <div class="header">
    <div class="back-button-container">
      <button class="back-button" on:click={goBack}>
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <line x1="19" y1="12" x2="5" y2="12"></line>
          <polyline points="12 19 5 12 12 5"></polyline>
        </svg>
        Back to Computer Details
      </button>
    </div>
    
    <h1>Service Quotation</h1>
    
    <div class="action-buttons">
      <button class="save-button" on:click={saveQuotation}>
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"></path>
          <polyline points="17 21 17 13 7 13 7 21"></polyline>
          <polyline points="7 3 7 8 15 8"></polyline>
        </svg>
        Save Quotation
      </button>
    </div>
  </div>
  
  {#if showSuccessMessage}
    <div class="alert success">
      <span>{successMessage}</span>
      <button class="close-button" on:click={() => showSuccessMessage = false}>&times;</button>
    </div>
  {/if}
  
  {#if showErrorMessage}
    <div class="alert error">
      <span>{errorMessage}</span>
      <button class="close-button" on:click={() => showErrorMessage = false}>&times;</button>
    </div>
  {/if}
  
  {#if data.error}
    <div class="error-box">
      {data.error}
    </div>
  {:else if !data.computer}
    <div class="loading">Loading...</div>
  {:else}
    <div class="content">
      <div class="basic-info-section">
        <div class="section-toolbar">
          <div class="toolbar-left">
            <h2>Computer and Contract Information</h2>
          </div>
          <div class="toolbar-right">
            <button class="add-button" on:click={addPackageToQuotation}>
              Add Driveline Quote to Quotation List
            </button>
            <button class="clear-button" on:click={clearQuotationList}>
              Clear Quotation List
            </button>
            <button class="clear-button" on:click={clearInputs}>
              Clear Inputs
            </button>
          </div>
        </div>
        
        <div class="info-grid">
          <div class="info-column">
            <div class="info-row">
              <div class="info-label">Desired Contract Length (Mnt):</div>
              <div class="info-value">
                <input 
                  type="number" 
                  bind:value={data.quotationPackages.basicInfo.desiredContractLength} 
                  min="1" 
                  max="60"
                />
              </div>
            </div>
            <div class="info-row">
              <div class="info-label">Desired Contract Start Date:</div>
              <div class="info-value">{formatDate(data.quotationPackages.basicInfo.desiredContractStartDate)}</div>
            </div>
            <div class="info-row">
              <div class="info-label">Contract Number:</div>
              <div class="info-value">
                <input 
                  type="text" 
                  bind:value={data.quotationPackages.basicInfo.contractNumber}
                />
              </div>
            </div>
          </div>
          
          <div class="info-column">
            <div class="info-row">
              <div class="info-label">Serial Number:</div>
              <div class="info-value">{data.computer.serialNumber}</div>
            </div>
            <div class="info-row">
              <div class="info-label">Product Designation:</div>
              <div class="info-value">{data.computer.productDesignation}</div>
            </div>
            <div class="info-row">
              <div class="info-label">Delivery Date:</div>
              <div class="info-value">{formatDate(data.quotationPackages.basicInfo.deliveryDate)}</div>
            </div>
          </div>
          
          <div class="info-column">
            <div class="info-row">
              <div class="info-label">Engine Hp (Hp):</div>
              <div class="info-value">{data.computer.engineHp}</div>
            </div>
            <div class="info-row">
              <div class="info-label">Trac Hp (Hp):</div>
              <div class="info-value">{data.computer.tracHp}</div>
            </div>
            <div class="info-row">
              <div class="info-label">Contract Sign Level:</div>
              <div class="info-value">{data.quotationPackages.basicInfo.contractSignLevel}</div>
            </div>
          </div>
          
          <div class="info-column">
            <div class="info-row">
              <div class="info-label">First Run Date:</div>
              <div class="info-value">{formatDate(data.quotationPackages.basicInfo.firstRunDate)}</div>
            </div>
            <div class="info-row">
              <div class="info-label">Engine Run Hours (hr):</div>
              <div class="info-value">{data.computer.hoursAtContractStart}</div>
            </div>
            <div class="info-row">
              <div class="info-label">Primary Spare:</div>
              <div class="info-value">
                <label class="checkbox-container">
                  <input 
                    type="checkbox" 
                    bind:checked={data.quotationPackages.basicInfo.primarySpare} 
                  />
                  <span class="checkmark"></span>
                </label>
              </div>
            </div>
          </div>
          
          <div class="info-column">
            <div class="info-row">
              <div class="info-label">Contract Total (Approx):</div>
              <div class="info-value total-value">{grandTotal.toFixed(2)} EUR</div>
            </div>
            <div class="info-row">
              <div class="info-label">Parts:</div>
              <div class="info-value">
                {(repairPackagesTotal + replacementServicesTotal).toFixed(2)} EUR
              </div>
            </div>
            <div class="info-row">
              <div class="info-label">Labour:</div>
              <div class="info-value">
                <input 
                  type="number" 
                  bind:value={data.quotationPackages.basicInfo.labourRate} 
                  min="0"
                  step="0.01"
                />
                EUR
              </div>
            </div>
            <div class="info-row">
              <div class="info-label">Travel:</div>
              <div class="info-value">Included</div>
            </div>
            <div class="info-row">
              <div class="info-label">Services & Coverage:</div>
              <div class="info-value">{(baseContractTotal + supportServicesTotal).toFixed(2)} EUR</div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="packages-section">
        <h2>Service Packages</h2>
        
        <div class="section-subtitle">Base Contract Offering</div>
        <QuotationGrid 
          packages={data.quotationPackages.baseContract}
          on:togglePackage={(e) => togglePackage('baseContract', e)}
        />
        
        <div class="section-subtitle">Dealer Add-Ons</div>
        <QuotationGrid 
          packages={data.quotationPackages.repairPackages}
          on:togglePackage={(e) => togglePackage('repairPackages', e)}
        />
        
        <div class="section-subtitle">Support Services</div>
        <QuotationGrid 
          packages={data.quotationPackages.supportServices}
          on:togglePackage={(e) => togglePackage('supportServices', e)}
        />
        
        <div class="section-subtitle">Replacement Plan</div>
        <QuotationGrid 
          packages={data.quotationPackages.replacementServices}
          on:togglePackage={(e) => togglePackage('replacementServices', e)}
        />
      </div>
    </div>
  {/if}
</div>

<style>
  .container {
    padding: 1rem;
    max-width: 1400px;
    margin: 0 auto;
  }
  
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
  }
  
  .back-button-container {
    flex: 1;
  }
  
  .back-button {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: none;
    border: none;
    color: #2196f3;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 4px;
  }
  
  .back-button:hover {
    background-color: #e3f2fd;
  }
  
  h1 {
    flex: 2;
    text-align: center;
    margin: 0;
  }
  
  .action-buttons {
    flex: 1;
    display: flex;
    justify-content: flex-end;
    gap: 0.5rem;
  }
  
  .save-button {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background-color: #4caf50;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
  }
  
  .save-button:hover {
    background-color: #388e3c;
  }
  
  .error-box {
    background-color: #ffebee;
    color: #c62828;
    padding: 1rem;
    border-radius: 4px;
    margin-bottom: 1rem;
  }
  
  .loading {
    padding: 2rem;
    text-align: center;
    color: #757575;
  }
  
  .alert {
    padding: 1rem;
    border-radius: 4px;
    margin-bottom: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .alert.success {
    background-color: #e8f5e9;
    color: #2e7d32;
  }
  
  .alert.error {
    background-color: #ffebee;
    color: #c62828;
  }
  
  .close-button {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: inherit;
  }
  
  .content {
    margin-bottom: 2rem;
  }
  
  .section-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
  }
  
  .toolbar-right {
    display: flex;
    gap: 0.5rem;
  }
  
  .add-button {
    background-color: #2196f3;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
  }
  
  .add-button:hover {
    background-color: #1976d2;
  }
  
  .clear-button {
    background-color: #f5f5f5;
    color: #333;
    border: 1px solid #ddd;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
  }
  
  .clear-button:hover {
    background-color: #e0e0e0;
  }
  
  /* CSS Grid layout for the basic info section */
  .info-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 1rem;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    padding: 1rem;
    background-color: #fafafa;
    margin-bottom: 2rem;
  }
  
  .info-column {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
  }
  
  .info-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.5rem;
    align-items: center;
  }
  
  .info-label {
    font-weight: 500;
    color: #666;
  }
  
  .info-value {
    font-weight: 400;
    color: #333;
  }
  
  .info-value input[type="text"],
  .info-value input[type="number"] {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
  }
  
  .total-value {
    font-weight: 700;
    color: #2e7d32;
  }
  
  .packages-section {
    margin-top: 2rem;
  }
  
  .section-subtitle {
    font-size: 1.1rem;
    font-weight: 600;
    margin-top: 1.5rem;
    margin-bottom: 0.5rem;
    color: #333;
  }
  
  h2 {
    color: #333;
    margin-top: 0;
    margin-bottom: 1rem;
    border-bottom: 1px solid #e0e0e0;
    padding-bottom: 0.5rem;
  }
  
  /* Checkbox styling */
  .checkbox-container {
    position: relative;
    display: block;
    width: 22px;
    height: 22px;
    cursor: pointer;
  }
  
  .checkbox-container input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
  }
  
  .checkmark {
    position: absolute;
    top: 0;
    left: 0;
    height: 22px;
    width: 22px;
    background-color: #eee;
    border: 1px solid #ddd;
    border-radius: 3px;
  }
  
  .checkbox-container:hover input ~ .checkmark {
    background-color: #ccc;
  }
  
  .checkbox-container input:checked ~ .checkmark {
    background-color: #2196F3;
    border-color: #2196F3;
  }
  
  .checkmark:after {
    content: "";
    position: absolute;
    display: none;
  }
  
  .checkbox-container input:checked ~ .checkmark:after {
    display: block;
  }
  
  .checkbox-container .checkmark:after {
    left: 7px;
    top: 3px;
    width: 6px;
    height: 12px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
  }
  
  /* Responsive adjustments */
  @media (max-width: 1200px) {
    .info-grid {
      grid-template-columns: repeat(3, 1fr);
    }
  }
  
  @media (max-width: 768px) {
    .header {
      flex-direction: column;
      gap: 1rem;
      align-items: flex-start;
    }
    
    .back-button-container,
    h1,
    .action-buttons {
      flex: initial;
      width: 100%;
    }
    
    h1 {
      text-align: left;
    }
    
    .section-toolbar {
      flex-direction: column;
      align-items: flex-start;
      gap: 1rem;
    }
    
    .toolbar-right {
      flex-direction: column;
      width: 100%;
    }
    
    .info-grid {
      grid-template-columns: 1fr;
    }
    
    .info-row {
      grid-template-columns: 1fr;
      gap: 0.25rem;
    }
  }
</style>
