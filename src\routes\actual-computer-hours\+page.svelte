<script lang="ts">
  import { onMount } from 'svelte';
  import ActualComputerHoursGrid from '$lib/components/grids/ActualComputerHoursGrid.svelte';
  import { goto } from '$app/navigation';

  let actualComputerHours = [];
  let loading = true;
  let error = '';
  let showAddModal = false;
  let form = {
    computerId: '',
    activity: '',
    hasFixed: false,
    hours: '',
    isIdle: false,
    createdAt: '',
    updatedAt: '',
    ReportDate: '',
    ReportTime: '',
    StopDateTime: ''
  };
  let errorMsg = '';
  let saving = false;

  // Add logging utility
  function logData(label, data) {
    console.log(`🔶 ${label} 🔶`);
    console.group(label);
    if (Array.isArray(data)) {
      console.log(`Array with ${data.length} items`);
      console.table(data);
      
      // Log more detailed information about each item
      data.forEach((item, index) => {
        console.group(`Item ${index + 1}`);
        for (const [key, value] of Object.entries(item)) {
          console.log(`${key}: ${value}`);
        }
        console.groupEnd();
      });
    } else if (data && typeof data === 'object') {
      console.table([data]);
    } else {
      console.log(data);
    }
    console.groupEnd();
    console.log(`🔶 END ${label} 🔶`);
  }

  async function fetchHours() {
    console.log(`[${new Date().toISOString()}] Starting fetchHours()`);
    loading = true;
    try {
      console.log('Fetching data from /api/actual-computer-hours');
      const res = await fetch('/api/actual-computer-hours');
      if (!res.ok) {
        const errorText = await res.text();
        console.error('API error response:', errorText);
        throw new Error('Failed to fetch data from API');
      }
      
      // Get the hours data
      const data = await res.json();
      console.log(`Received ${data.length} records from API`);
      logData('Raw API Response', data);
      
      // Process to ensure ReportDate and ReportTime are present
      console.log('Processing data to ensure ReportDate and ReportTime fields');
      actualComputerHours = data.map(row => {
        const newRow = { ...row };
        
        // Remove ReportDateTime field to ensure it doesn't appear in the grid
        if (newRow.ReportDateTime) {
          console.log(`Removing ReportDateTime field from row ${row._id}`);
          delete newRow.ReportDateTime;
        }
        
        if (newRow.reportDateTime) {
          console.log(`Removing reportDateTime field from row ${row._id}`);
          delete newRow.reportDateTime;
        }
        
        // Set current date and time if missing
        const now = new Date();
        
        // Ensure ReportDate is present and properly formatted
        if (!newRow.ReportDate || newRow.ReportDate === '') {
          console.log(`Adding missing ReportDate for row ${row._id}`);
          newRow.ReportDate = now.toISOString().split('T')[0];
        }
        
        // Ensure ReportTime is present and properly formatted
        if (!newRow.ReportTime || newRow.ReportTime === '') {
          console.log(`Adding missing ReportTime for row ${row._id}`);
          const hours = now.getHours().toString().padStart(2, '0');
          const minutes = now.getMinutes().toString().padStart(2, '0');
          newRow.ReportTime = `${hours}:${minutes}`;
        }
        
        // Log the processed row
        console.log(`Processed row ${row._id}: ReportDate=${newRow.ReportDate}, ReportTime=${newRow.ReportTime}`);
        
        return newRow;
      });
      
      // Log the final processed data
      logData('Processed Data for Display', actualComputerHours);
      console.log(`[${new Date().toISOString()}] Completed fetchHours() successfully`);
      loading = false;
    } catch (err) {
      console.error(`[${new Date().toISOString()}] Error in fetchHours():`, err);
      error = err.message;
      loading = false;
    }
  }

  async function deleteHour(id) {
    console.log(`[${new Date().toISOString()}] Starting deleteHour(${id})`);
    if (!confirm('Are you sure you want to delete this record?')) {
      console.log('Deletion cancelled by user');
      return;
    }
    
    try {
      console.log(`Sending DELETE request for id ${id}`);
      const res = await fetch(`/api/actual-computer-hours?id=${id}`, {
        method: 'DELETE'
      });
      
      if (!res.ok) {
        const errorText = await res.text();
        console.error('API error response:', errorText);
        throw new Error('Failed to delete');
      }
      
      console.log(`Successfully deleted record ${id}`);
      fetchHours(); // Refresh the list
    } catch (err) {
      console.error(`[${new Date().toISOString()}] Error in deleteHour():`, err);
      alert(`Error deleting record: ${err.message}`);
    }
  }

  function openAddModal() {
    console.log(`[${new Date().toISOString()}] Opening add modal`);
    form = {
      computerId: '',
      activity: '',
      hasFixed: false,
      hours: '',
      isIdle: false,
      createdAt: '',
      updatedAt: '',
      ReportDate: '',
      ReportTime: '',
      StopDateTime: ''
    };
    showAddModal = true;
  }

  function closeAddModal() {
    console.log(`[${new Date().toISOString()}] Closing add modal`);
    showAddModal = false;
  }

  async function saveNew() {
    console.log(`[${new Date().toISOString()}] Starting saveNew()`);
    logData('Form Data', form);
    
    saving = true;
    errorMsg = '';
    
    try {
      // Validation
      if (!form.computerId) {
        throw new Error('Computer ID is required');
      }
      if (!form.hours) {
        throw new Error('Hours is required');
      }
      
      // Default activity to 'Running' if not specified
      if (!form.activity) {
        console.log('Setting default activity to Running');
        form.activity = 'Running';
      }
      
      // Setting current date/time
      const now = new Date();
      form.createdAt = now;
      form.updatedAt = now;
      form.ReportDate = now.toISOString().split('T')[0];
      form.ReportTime = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;
      
      console.log('Prepared form data for saving:', form);
      
      // Save to database
      console.log('Sending POST request to /api/actual-computer-hours');
      const res = await fetch('/api/actual-computer-hours', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(form)
      });
      
      if (!res.ok) {
        const errorData = await res.json();
        console.error('API error response:', errorData);
        throw new Error(errorData.error || 'Failed to save');
      }
      
      const result = await res.json();
      console.log(`Successfully created new record with ID: ${result.insertedId}`);
      
      closeAddModal();
      fetchHours(); // Refresh the list
    } catch (err) {
      console.error(`[${new Date().toISOString()}] Error in saveNew():`, err);
      errorMsg = err.message;
    } finally {
      saving = false;
    }
  }

  onMount(() => {
    console.log(`[${new Date().toISOString()}] Component mounted, fetching hours data`);
    fetchHours();
  });
</script>

<div class="page-container">
  <div class="heading-row">
    <h1>All Actual Computer Hours (Full Details)</h1>
    <div class="button-group">
      <button class="add-btn" on:click={openAddModal}>Quick Add</button>
      <button class="add-btn" on:click={() => goto('/actual-computer-hours/add?computerId=67ff6037f3a1959426e20d29')}>Add New</button>
    </div>
  </div>

{#if showAddModal}
  <div class="modal-overlay">
    <div class="modal-window">
      <h2>Add New Actual Computer Hours</h2>
      <form on:submit|preventDefault={saveNew}>
        <div class="form-row">
          <label for="computerId">Computer ID:</label>
          <input id="computerId" type="text" bind:value={form.computerId} placeholder="Required" />
        </div>
        <div class="form-row">
          <label for="activity">Activity:</label>
          <input id="activity" type="text" bind:value={form.activity} placeholder="e.g., Running" />
        </div>
        <div class="form-row">
          <label for="hours">Hours:</label>
          <input id="hours" type="number" bind:value={form.hours} min="0" />
        </div>
        <div class="form-row checkbox-row">
          <label for="hasFixed">Has Fixed:</label>
          <input id="hasFixed" type="checkbox" bind:checked={form.hasFixed} />
        </div>
        <div class="form-row checkbox-row">
          <label for="isIdle">Is Idle:</label>
          <input id="isIdle" type="checkbox" bind:checked={form.isIdle} />
        </div>
        <div class="form-row">
          <label for="ReportDate">Report Date:</label>
          <input id="ReportDate" type="date" bind:value={form.ReportDate} />
        </div>
        <div class="form-row">
          <label for="ReportTime">Report Time:</label>
          <input id="ReportTime" type="time" bind:value={form.ReportTime} />
        </div>
        <div class="form-buttons">
          <button type="button" on:click={closeAddModal}>Cancel</button>
          <button type="submit" disabled={saving}>{saving ? 'Saving...' : 'Save'}</button>
        </div>
        {#if errorMsg}
          <div class="error">{errorMsg}</div>
        {/if}
      </form>
    </div>
  </div>
{/if}

{#if loading}
  <p>Loading...</p>
{:else if error}
  <p style="color:red">{error}</p>
{:else}
  <h1>All Actual Computer Hours (Full Details)</h1>
  
  <!-- Display using the grid component -->
  <ActualComputerHoursGrid {actualComputerHours} on:add={openAddModal} />
  
  <!-- Alternate detailed view as a table -->
  <div class="table-wrapper">
    <table class="data-table">
      <thead>
        <tr>
          <th>#</th>
          <th>Activity</th>
          <th>Hours</th>
          <th>Report Date</th>
          <th>Report Time</th>
          <th>Computer ID</th>
          <th>Has Fixed</th>
          <th>Is Idle</th>
          <th>Created At</th>
          <th>Updated At</th>
          <th>Actions</th>
        </tr>
      </thead>
      <tbody>
        {#each actualComputerHours as row, i}
          <tr>
            <td>{i + 1}</td>
            <td>{row.activity || 'Running'}</td>
            <td>{row.hours || 0}</td>
            <td class="date-field">{row.ReportDate || '-'}</td>
            <td class="time-field">{row.ReportTime || '-'}</td>
            <td>{row.computerId}</td>
            <td>{row.hasFixed ? 'Yes' : 'No'}</td>
            <td>{row.isIdle ? 'Yes' : 'No'}</td>
            <td>{row.createdAt ? new Date(row.createdAt).toLocaleString() : '-'}</td>
            <td>{row.updatedAt ? new Date(row.updatedAt).toLocaleString() : '-'}</td>
            <td>
              <button class="action-button edit" on:click={() => goto(`/actual-computer-hours/${row._id}`)}>Edit</button>
              <button class="action-button delete" on:click={() => deleteHour(row._id)}>Delete</button>
            </td>
          </tr>
        {/each}
      </tbody>
    </table>
  </div>
  
  <!-- Bottom action buttons -->
  <div class="bottom-actions">
    <button class="action-button primary" on:click={openAddModal}>
      Add New Hours
    </button>
    <button class="action-button secondary" on:click={fetchHours}>
      Refresh List
    </button>
  </div>
{/if}
</div>

<style>
.page-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2em;
}
.heading-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1.2em;
}
.button-group {
  display: flex;
  gap: 0.8em;
}
.add-btn {
  background: #059669;
  color: #fff;
  border: none;
  border-radius: 5px;
  font-weight: 600;
  padding: 0.6em 1.2em;
  font-size: 1em;
  cursor: pointer;
  transition: background 0.2s;
}
.add-btn:hover {
  background: #047857;
}
.modal-overlay {
  position: fixed;
  top: 0; left: 0; right: 0; bottom: 0;
  background: rgba(0,0,0,0.18);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}
.modal-window {
  background: #fff;
  border-radius: 10px;
  padding: 2.2em 2.5em 2em 2.5em;
  box-shadow: 0 6px 30px rgba(30,42,80,0.14);
  min-width: 350px;
  max-width: 95vw;
}
.modal-window h2 {
  margin-top: 0;
  margin-bottom: 1.2em;
}
.modal-window form label {
  display: block;
  margin-bottom: 0.6em;
  font-weight: 500;
}
.modal-window form input[type="text"],
.modal-window form input[type="number"],
.modal-window form input[type="datetime-local"] {
  width: 100%;
  padding: 0.4em 0.7em;
  border: 1px solid #cbd5e1;
  border-radius: 4px;
  font-size: 1.03em;
  background: #f8fafc;
  margin-top: 0.2em;
}
.modal-actions {
  display: flex;
  gap: 1.2em;
  margin-top: 1em;
}
.bottom-actions {
  display: flex;
  gap: 0.8em;
  margin-top: 1em;
}
.table-wrapper {
  margin-top: 1em;
}
.data-table {
  width: 100%;
  border-collapse: collapse;
}
.data-table th, .data-table td {
  border: 1px solid #ddd;
  padding: 0.6em 0.8em;
  text-align: left;
}
.data-table th {
  background: #f0f0f0;
}
.date-field, .time-field {
  font-weight: 600;
}
.action-button {
  background: #059669;
  color: #fff;
  border: none;
  border-radius: 5px;
  font-weight: 600;
  padding: 0.6em 1.2em;
  font-size: 1em;
  cursor: pointer;
  transition: background 0.2s;
}
.action-button:hover {
  background: #047857;
}
.action-button.edit {
  background: #4CAF50;
}
.action-button.edit:hover {
  background: #3e8e41;
}
.action-button.delete {
  background: #e74c3c;
}
.action-button.delete:hover {
  background: #c0392b;
}
.action-button.primary {
  background: #059669;
}
.action-button.primary:hover {
  background: #047857;
}
.action-button.secondary {
  background: #4CAF50;
}
.action-button.secondary:hover {
  background: #3e8e41;
}
</style>
