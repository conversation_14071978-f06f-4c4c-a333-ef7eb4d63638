<script>
  import { page } from '$app/stores';
  import GridLayout from '$lib/components/GridLayout.svelte';
  import FilterGrid from '$lib/components/FilterGrid.svelte';

  /** @type {import('./$types').PageData} */
  export let data;

  /**
   * @typedef {Object} ServiceCodeAndActionTypeData
   * @property {string} _id - MongoDB ObjectId as string
   * @property {string} ProductValidityGroup - Product validity group identifier
   * @property {string} ActivityPurpose - Purpose of the activity
   * @property {string} ServiceActivityLabel - Service activity label
   * @property {string} ServiceCode - Service code identifier
   * @property {string} ActionType - Action type identifier
   * @property {number} PartNumber - Part number
   * @property {string} UnitOfMeasure - Unit of measure
   * @property {number} Quantity - Quantity
   * @property {number} InternalNoOfHours - Internal number of hours
   * @property {number|null} InternalNoOfMonths - Internal number of months
   */

  // Collection info
  const collection = 'ServiceCodeAndActionType';
  const filterlist = $page.url.searchParams.get('filterlist') || 'ProductValidityGroup';

  /** @type {ServiceCodeAndActionTypeData[]} */
  let items = /** @type {ServiceCodeAndActionTypeData[]} */ (data.items || []);

  // Setup filter fields for the new filter system
  /** @type {import('$lib/components/FilterGrid.svelte').FilterField[]} */
  let filterFields = [
    {
      id: 'ProductValidityGroup',
      name: 'Product Validity Group',
      value: $page.url.searchParams.get('ProductValidityGroup') || ''
    },
    {
      id: 'ActivityPurpose',
      name: 'Activity Purpose',
      value: $page.url.searchParams.get('ActivityPurpose') || ''
    },
    {
      id: 'ServiceActivityLabel',
      name: 'Service Activity Label',
      value: $page.url.searchParams.get('ServiceActivityLabel') || ''
    },
    {
      id: 'ServiceCode',
      name: 'Service Code',
      value: $page.url.searchParams.get('ServiceCode') || ''
    },
    {
      id: 'ActionType',
      name: 'Action Type',
      value: $page.url.searchParams.get('ActionType') || ''
    }
  ];

  // Pagination settings
  let currentPage = parseInt($page.url.searchParams.get('page') || '1');
  let itemsPerPage = parseInt($page.url.searchParams.get('limit') || '50'); // Default 50 items per page
  let totalItems = items.length;
  let totalPages = Math.ceil(totalItems / itemsPerPage);

  // Calculate pagination values
  $: startIndex = (currentPage - 1) * itemsPerPage;
  $: endIndex = Math.min(startIndex + itemsPerPage, items.length);
  $: displayedItems = filteredItems.slice(startIndex, endIndex);
  $: totalPages = Math.ceil(filteredItems.length / itemsPerPage);
  $: paginationRange = getPaginationRange(currentPage, totalPages);

  /**
   * Navigate to the specified page number
   * @param {number} pageNum - The page number to navigate to
   */
  function goToPage(pageNum) {
    if (pageNum < 1 || pageNum > totalPages) return;
    const url = new URL(window.location.href);
    url.searchParams.set('page', pageNum.toString());
    window.location.href = url.toString();
  }

  /**
   * Generate an array of page numbers to display for pagination
   * @param {number} current - The current page number
   * @param {number} total - The total number of pages
   * @returns {Array<number|string>} - Array of page numbers and ellipsis strings
   */
  function getPaginationRange(current, total) {
    const delta = 2; // Number of pages to show before and after current page
    const range = [];

    // Always include first page
    range.push(1);

    // Calculate start and end of range
    const rangeStart = Math.max(2, current - delta);
    const rangeEnd = Math.min(total - 1, current + delta);

    // Add ellipsis after first page if needed
    if (rangeStart > 2) range.push('...');

    // Add pages in range
    for (let i = rangeStart; i <= rangeEnd; i++) {
      range.push(i);
    }

    // Add ellipsis before last page if needed
    if (rangeEnd < total - 1) range.push('...');

    // Always include last page if it's not the first page
    if (total > 1) range.push(total);

    return range;
  }

  // Function to refresh data with a full page reload
  async function refreshData() {
    try {
      window.location.reload();
    } catch (error) {
      console.error('Error refreshing page:', error);
    }
  }

  // Function to filter items
  /** @param {string} value - The filter value to apply */
  async function applyFilter(value) {
    try {
      const url = new URL(window.location.origin + '/liststd1ServiceCodeAndActionType');

      // Reset to page 1 when applying a new filter
      url.searchParams.set('page', '1');

      if (value) {
        url.searchParams.set('filterlist', filterlist);
        url.searchParams.set('filterValue', value);
      }

      window.location.href = url.toString();
    } catch (error) {
      console.error('Error applying filter:', error);
    }
  }

  /**
   * Change the number of items displayed per page
   * @param {Event} event - The change event from the select element
   */
  function changeItemsPerPage(event) {
    // Type assertion to make TypeScript happy
    const select = /** @type {HTMLSelectElement} */ (event.target);
    const newLimit = select.value;

    const url = new URL(window.location.href);
    url.searchParams.set('limit', newLimit);
    url.searchParams.set('page', '1'); // Reset to page 1 when changing limit
    window.location.href = url.toString();
  }

  // Initialize searchTerm from URL parameter
  let searchTerm = $page.url.searchParams.get('filterValue') || '';

  // Simple filtering function
  $: filteredItems = items;
</script>

<div class="container">
  <div class="header-content">
    <div class="title-actions">
      <h1>{collection}</h1>
      <button class="btn-primary refresh-btn" on:click={refreshData}>
        <svg xmlns="http://www.w3.org/2000/svg" class="refresh-icon" viewBox="0 0 20 20" fill="currentColor" width="16" height="16">
          <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd" />
        </svg>
        Refresh Data
      </button>
    </div>

    <!-- Legacy filter system (hidden but kept for backward compatibility) -->
    <div class="legacy-filter" style="display: none;">
      <div class="search-container">
        <form on:submit|preventDefault={() => applyFilter(searchTerm)}>
          <div class="search-grid">
            <input
              type="text"
              placeholder="Search by {filterlist}..."
              class="search-input"
              bind:value={searchTerm}
            />
            <button type="submit" class="btn-search">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" width="20" height="20">
                <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
              </svg>
              Search
            </button>
            {#if $page.url.searchParams.get('filterValue')}
              <button
                type="button"
                class="btn-secondary"
                on:click={() => applyFilter('')}
              >
                Clear Filter
              </button>
            {/if}
          </div>
        </form>
      </div>
    </div>
  </div>

  {#if filteredItems.length === 0}
    <div class="empty-state">
      <div class="empty-icon">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor" width="48" height="48">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" />
        </svg>
      </div>
      <h2>No service code and action type found</h2>
      <p>There are no items matching your criteria. Try adjusting your filters.</p>
    </div>
  {:else}
    <div class="grid-wrapper">
      <div class="total-items-info">
        <div class="item-count">
          <svg xmlns="http://www.w3.org/2000/svg" class="items-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor" width="18" height="18">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
          </svg>
          Showing <span class="item-highlight">{startIndex + 1}</span> to <span class="item-highlight">{endIndex}</span> of <span class="item-highlight">{filteredItems.length}</span> items
        </div>
        <div class="items-per-page">
          <label for="items-per-page">Items per page:</label>
          <select id="items-per-page" class="per-page-select" on:change={changeItemsPerPage} value={itemsPerPage}>
            <option value="25">25</option>
            <option value="50">50</option>
            <option value="100">100</option>
            <option value="250">250</option>
            <option value="500">500</option>
          </select>
        </div>
      </div>

      <!-- Add filter grid aligned with the columns -->
      <FilterGrid bind:fields={filterFields} />

      <div class="grid-container">
        <div class="data-grid">
          <!-- Headers -->
          <div class="grid-header">Product Validity Group</div>
          <div class="grid-header">Activity Purpose</div>
          <div class="grid-header">Service Activity Label</div>
          <div class="grid-header">Service Code</div>
          <div class="grid-header">Action Type</div>
          <div class="grid-header">Part Number</div>
          <div class="grid-header">Unit of Measure</div>
          <div class="grid-header">Quantity</div>
          <div class="grid-header">Internal Hours</div>
          <div class="grid-header">Internal Months</div>
        </div>

        <!-- Data Rows - only show current page items -->
        <div class="data-grid">
          {#each displayedItems as item}
            <div class="grid-cell">{item.ProductValidityGroup}</div>
            <div class="grid-cell">{item.ActivityPurpose}</div>
            <div class="grid-cell">{item.ServiceActivityLabel}</div>
            <div class="grid-cell">{item.ServiceCode}</div>
            <div class="grid-cell">{item.ActionType}</div>
            <div class="grid-cell">{item.PartNumber}</div>
            <div class="grid-cell">{item.UnitOfMeasure}</div>
            <div class="grid-cell">{item.Quantity}</div>
            <div class="grid-cell">{item.InternalNoOfHours}</div>
            <div class="grid-cell">{item.InternalNoOfMonths === null ? '-' : item.InternalNoOfMonths}</div>
          {/each}
        </div>
      </div>
    </div>

    <!-- Pagination Controls -->
    {#if totalPages > 1}
      <div class="pagination">
        <button
          class="pagination-btn"
          on:click={() => goToPage(1)}
          disabled={currentPage === 1}
        >
          &laquo; First
        </button>
        <button
          class="pagination-btn"
          on:click={() => goToPage(currentPage - 1)}
          disabled={currentPage === 1}
        >
          &lsaquo; Previous
        </button>

        {#each paginationRange as page}
          {#if page === '...'}
            <span class="pagination-ellipsis">...</span>
          {:else}
            <button
              class="pagination-btn {currentPage === page ? 'active' : ''}"
              on:click={() => goToPage(Number(page))}
            >
              {page}
            </button>
          {/if}
        {/each}

        <button
          class="pagination-btn"
          on:click={() => goToPage(currentPage + 1)}
          disabled={currentPage === totalPages}
        >
          Next &rsaquo;
        </button>
        <button
          class="pagination-btn"
          on:click={() => goToPage(totalPages)}
          disabled={currentPage === totalPages}
        >
          Last &raquo;
        </button>
      </div>
    {/if}
  {/if}
</div>

<style>
  /* Base styles */
  .container {
    max-width: 1400px;
    margin: 0 auto; /* Center the container */
    padding: 2rem 1rem;
  }

  /* Grid Wrapper */
  .grid-wrapper {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
  }

  /* Header styles */
  .header-content {
    display: flex;
    flex-direction: column;
    margin-bottom: 1.5rem;
  }

  .title-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #edf2f7;
  }

  .header-content h1 {
    margin: 0;
    font-size: 1.75rem;
    font-weight: 600;
    color: #334155;
    display: flex;
    align-items: center;
  }

  .refresh-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    font-weight: 500;
    background-color: #3b82f6;
    color: white;
    border: none;
    border-radius: 0.25rem;
    transition: all 0.2s ease;
    cursor: pointer;
  }

  .refresh-btn:hover {
    background-color: #2563eb;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  }

  .refresh-icon {
    width: 16px;
    height: 16px;
  }

  /* Search container */
  .search-container {
    flex: 1;
    min-width: 280px;
  }

  .search-grid {
    display: grid;
    grid-template-columns: 1fr auto auto;
    gap: 0.5rem;
    align-items: center;
  }

  .search-input {
    padding: 0.5rem 1rem;
    border: 1px solid #ddd;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    width: 100%;
  }

  .search-input:focus {
    outline: none;
    border-color: #4a90e2;
    box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
  }

  /* Pagination and Data Info Styles */
  .total-items-info {
    display: grid;
    grid-template-columns: 1fr auto;
    align-items: center;
    margin-bottom: 1rem;
    font-size: 0.8125rem;
    color: #64748b;
    background-color: #f9fafb;
    border: 1px solid #edf2f7;
    border-radius: 0.5rem;
    padding: 0.75rem 1rem;
  }

  .item-count {
    display: flex;
    align-items: center;
  }

  .items-icon {
    margin-right: 0.5rem;
    color: #94a3b8;
  }

  .item-highlight {
    font-weight: 600;
    color: #475569;
    margin: 0 0.125rem;
  }

  .items-per-page {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .per-page-select {
    padding: 0.375rem 0.75rem;
    border: 1px solid #e2e8f0;
    border-radius: 0.25rem;
    background-color: white;
    font-size: 0.8125rem;
    color: #475569;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .per-page-select:hover {
    border-color: #cbd5e1;
  }

  .per-page-select:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.15);
  }

  .pagination {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
    margin: 1.5rem 0;
  }

  .pagination-btn {
    min-width: 2.5rem;
    height: 2.5rem;
    padding: 0 0.75rem;
    border: 1px solid #e2e8f0;
    border-radius: 0.25rem;
    background-color: white;
    font-size: 0.875rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #475569;
  }

  .pagination-btn:hover:not(:disabled) {
    background-color: #f8fafc;
    border-color: #cbd5e1;
  }

  .pagination-btn.active {
    background-color: #3b82f6;
    color: white;
    border-color: #3b82f6;
    font-weight: 600;
  }

  .pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .pagination-ellipsis {
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 2.5rem;
    color: #64748b;
    font-size: 0.875rem;
  }

  /* Button Styles */
  .btn-primary, .btn-secondary, .btn-search {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 1rem;
    border-radius: 0.25rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s, transform 0.1s;
    border: none;
    font-size: 0.875rem;
    gap: 0.5rem;
  }

  .btn-primary {
    background-color: #4a90e2;
    color: white;
  }

  .btn-primary:hover {
    background-color: #3a7bc8;
  }

  .btn-secondary {
    background-color: #f1f5f9;
    color: #64748b;
    border: 1px solid #e2e8f0;
  }

  .btn-secondary:hover {
    background-color: #e2e8f0;
  }

  .btn-search {
    background-color: #4a90e2;
    color: white;
    padding: 0.5rem 1rem;
  }

  .btn-search:hover {
    background-color: #3a7bc8;
  }

  /* Empty state */
  .empty-state {
    text-align: center;
    padding: 3rem 1rem;
    background-color: #f8fafc;
    border-radius: 0.5rem;
    margin-top: 2rem;
  }

  .empty-state h2 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #334155;
  }

  .empty-state p {
    color: #64748b;
    margin-bottom: 1.5rem;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
  }

  .empty-icon {
    display: flex;
    justify-content: center;
    margin-bottom: 1.5rem;
    color: #94a3b8;
  }

  /* Data Grid Styles */
  .grid-container {
    border: 1px solid #e2e8f0;
    border-radius: 0.5rem;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    background-color: white;
  }

  .data-grid {
    display: grid;
    grid-template-columns: repeat(10, 1fr);
    grid-auto-rows: minmax(48px, auto);
  }

  .grid-header {
    font-weight: 600;
    background-color: #f8fafc;
    border-bottom: 1px solid #e2e8f0;
    padding: 0.75rem 1rem;
    position: sticky;
    top: 0;
    z-index: 10;
    color: #334155;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
  }

  .grid-cell {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #f1f5f9;
    color: #4a5568;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    transition: background-color 0.15s ease;
  }

  /* Alternate row styling for better readability */
  .data-grid > .grid-cell:nth-child(20n+11),
  .data-grid > .grid-cell:nth-child(20n+12),
  .data-grid > .grid-cell:nth-child(20n+13),
  .data-grid > .grid-cell:nth-child(20n+14),
  .data-grid > .grid-cell:nth-child(20n+15),
  .data-grid > .grid-cell:nth-child(20n+16),
  .data-grid > .grid-cell:nth-child(20n+17),
  .data-grid > .grid-cell:nth-child(20n+18),
  .data-grid > .grid-cell:nth-child(20n+19),
  .data-grid > .grid-cell:nth-child(20n+20) {
    background-color: #f9fafb;
  }

  /* Responsive adjustments */
  @media (min-width: 768px) {
    .header-content {
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
    }

    .header-content h1 {
      margin-bottom: 0;
    }
  }
</style>
