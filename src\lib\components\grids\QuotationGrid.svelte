<script>
  import { createEventDispatcher } from 'svelte';
  
  // Props
  export let packages = [];
  export let title = '';
  export let showCosts = true;
  export let showOemImporter = true;
  export let showFleetOwner = true;
  export let showLevel = true;
  export let showRequired = true;
  export let currency = 'EUR';
  
  // Events
  const dispatch = createEventDispatcher();
  
  // Handle package selection
  function togglePackage(packageId) {
    dispatch('togglePackage', { id: packageId });
  }
  
  // Format currency display
  function formatCurrency(amount) {
    if (!amount) return '0.00';
    return parseFloat(amount).toFixed(2);
  }
  
  // Check mark display
  function getCheckDisplay(value) {
    return value ? '✓' : '';
  }
</script>

<div class="quotation-grid-container">
  {#if title}
    <div class="grid-title">{title}</div>
  {/if}
  
  <div class="quotation-grid">
    <div class="quotation-header" role="rowgroup">
      <div class="column level" role="columnheader">Level</div>
      <div class="column package" role="columnheader">Package</div>
      {#if showLevel}
        <div class="column service-id" role="columnheader">Service ID</div>
      {/if}
      <div class="column activity" role="columnheader">Activity</div>
      <div class="column include" role="columnheader">Include in Offer</div>
      {#if showRequired}
        <div class="column required" role="columnheader">Required</div>
      {/if}
      {#if showCosts}
        <div class="column cost" role="columnheader">Cost</div>
      {/if}
      {#if showOemImporter}
        <div class="column oem" role="columnheader">OEM Importer</div>
      {/if}
      {#if showFleetOwner}
        <div class="column fleet" role="columnheader">Fleet Owner</div>
      {/if}
      <div class="column customer" role="columnheader">Customer Specific</div>
      <div class="column rsp" role="columnheader">RSP</div>
    </div>
    
    {#if packages.length > 0}
      {#each packages as pack}
        <div class="quotation-row" role="row">
          <div class="column level" role="cell">{pack.id}</div>
          <div class="column package" role="cell">{pack.name}</div>
          {#if showLevel}
            <div class="column service-id" role="cell">{pack.packLevel || ''}</div>
          {/if}
          <div class="column activity" role="cell">{pack.activity || pack.subname || ''}</div>
          <div class="column include" role="cell">
            <label class="checkbox-container">
              <input 
                type="checkbox" 
                checked={pack.includeInOffer} 
                disabled={pack.required}
                on:change={() => togglePackage(pack.id)}
              />
              <span class="checkmark"></span>
            </label>
          </div>
          {#if showRequired}
            <div class="column required" role="cell">{pack.required ? 'Yes' : 'No'}</div>
          {/if}
          {#if showCosts}
            <div class="column cost" role="cell">{formatCurrency(pack.cost)} {currency}</div>
          {/if}
          {#if showOemImporter}
            <div class="column oem" role="cell">{getCheckDisplay(pack.oemImporter)}</div>
          {/if}
          {#if showFleetOwner}
            <div class="column fleet" role="cell">{getCheckDisplay(pack.fleetOwner)}</div>
          {/if}
          <div class="column customer" role="cell">{getCheckDisplay(pack.customerSpecific)}</div>
          <div class="column rsp" role="cell">{getCheckDisplay(pack.yes)}</div>
        </div>
      {/each}
    {:else}
      <div class="empty-message" role="row">
        <div role="cell">No packages available</div>
      </div>
    {/if}
  </div>
</div>

<style>
  /* CSS Grid-based layout, as per user preference */
  .quotation-grid-container {
    width: 100%;
    margin-bottom: 1.5rem;
  }
  
  .grid-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    padding: 0.5rem;
    background-color: #f5f5f5;
    border-radius: 4px 4px 0 0;
  }
  
  .quotation-grid {
    display: grid;
    grid-template-columns: 1fr;
    border: 1px solid #e0e0e0;
    border-radius: 0 0 4px 4px;
    overflow: hidden;
  }
  
  .quotation-header {
    display: grid;
    grid-template-columns: minmax(50px, 0.5fr) minmax(150px, 1.5fr) minmax(90px, 0.8fr) minmax(150px, 1.5fr) minmax(80px, 0.8fr) minmax(80px, 0.8fr) minmax(80px, 0.8fr) minmax(80px, 0.8fr) minmax(80px, 0.8fr) minmax(100px, 1fr) minmax(50px, 0.5fr);
    background-color: #f5f5f5;
    font-weight: 600;
    border-bottom: 2px solid #ddd;
  }
  
  .quotation-row {
    display: grid;
    grid-template-columns: minmax(50px, 0.5fr) minmax(150px, 1.5fr) minmax(90px, 0.8fr) minmax(150px, 1.5fr) minmax(80px, 0.8fr) minmax(80px, 0.8fr) minmax(80px, 0.8fr) minmax(80px, 0.8fr) minmax(80px, 0.8fr) minmax(100px, 1fr) minmax(50px, 0.5fr);
    border-bottom: 1px solid #e0e0e0;
  }
  
  .quotation-row:nth-child(even) {
    background-color: #fafafa;
  }
  
  .quotation-row:hover {
    background-color: #f0f7ff;
  }
  
  .column {
    padding: 0.75rem 0.5rem;
    display: flex;
    align-items: center;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  
  .column.required,
  .column.oem,
  .column.fleet,
  .column.customer,
  .column.rsp {
    justify-content: center;
  }
  
  .column.cost {
    justify-content: flex-end;
  }
  
  .column.include {
    justify-content: center;
  }
  
  /* Responsive adjustments */
  @media (max-width: 1200px) {
    .quotation-header,
    .quotation-row {
      grid-template-columns: minmax(50px, 0.5fr) minmax(150px, 1.5fr) minmax(150px, 1.5fr) minmax(80px, 0.8fr) minmax(80px, 0.8fr) minmax(80px, 0.8fr) minmax(100px, 1fr);
    }
    
    .column.service-id,
    .column.oem,
    .column.fleet,
    .column.required {
      display: none;
    }
  }
  
  @media (max-width: 768px) {
    .quotation-header,
    .quotation-row {
      grid-template-columns: minmax(50px, 0.5fr) minmax(150px, 1.5fr) minmax(80px, 0.8fr) minmax(80px, 0.8fr);
    }
    
    .column.activity,
    .column.cost,
    .column.customer {
      display: none;
    }
  }
  
  /* Checkbox styling */
  .checkbox-container {
    position: relative;
    display: block;
    width: 22px;
    height: 22px;
    cursor: pointer;
  }
  
  .checkbox-container input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
  }
  
  .checkmark {
    position: absolute;
    top: 0;
    left: 0;
    height: 22px;
    width: 22px;
    background-color: #eee;
    border: 1px solid #ddd;
    border-radius: 3px;
  }
  
  .checkbox-container:hover input ~ .checkmark {
    background-color: #ccc;
  }
  
  .checkbox-container input:checked ~ .checkmark {
    background-color: #2196F3;
    border-color: #2196F3;
  }
  
  .checkbox-container input:disabled ~ .checkmark {
    background-color: #f5f5f5;
    border-color: #ddd;
    cursor: not-allowed;
  }
  
  .checkmark:after {
    content: "";
    position: absolute;
    display: none;
  }
  
  .checkbox-container input:checked ~ .checkmark:after {
    display: block;
  }
  
  .checkbox-container .checkmark:after {
    left: 7px;
    top: 3px;
    width: 6px;
    height: 12px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
  }
  
  .empty-message {
    grid-column: 1 / -1;
    padding: 2rem;
    text-align: center;
    color: #757575;
  }
</style>
